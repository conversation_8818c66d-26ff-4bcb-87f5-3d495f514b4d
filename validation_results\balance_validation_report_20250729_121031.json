{"validation_timestamp": "2025-07-29T12:10:31.882402", "validation_type": "historical_momentum_balance_ratios", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000000, "temporal_validation": true, "min_context_sample_size": 150, "significance_threshold": 0.05, "balance_ratios_tested": [[0.2, 0.8], [0.3, 0.7], [0.4, 0.6], [0.5, 0.5], [0.6, 0.4], [0.7, 0.3], [0.8, 0.2]]}, "context_results": {"Clay_Set1_Mid": {"optimal_balance": {"historical_ratio": 0.3, "momentum_ratio": 0.7, "accuracy": 0.6530688979591835, "confidence_interval_95": [0.6122448979591837, 0.6938775510204082], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 147}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.5374149659863946, "std_accuracy": 0.03848200169722706, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.6530612244897959, "std_accuracy": 0.03332639105827453, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.5714285714285714, "std_accuracy": 0.028861501272920337, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.6258503401360543, "std_accuracy": 0.04193478913584338, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.6122448979591837, "std_accuracy": 0.03332639105827453, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.5306122448979592, "std_accuracy": 0.08817334283548109, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.5102040816326531, "std_accuracy": 0.04408667141774054, "total_folds": 3}}, "recommendation": "EXCELLENT - High accuracy (0.653) with statistical significance"}, "Clay_Set2_Mid": {"optimal_balance": {"historical_ratio": 0.3, "momentum_ratio": 0.7, "accuracy": 0.6511742790697672, "confidence_interval_95": [0.5348837209302325, 0.7441860465116279], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 129}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.5348837209302326, "std_accuracy": 0.050238299987657845, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.6511627906976744, "std_accuracy": 0.08701528806451027, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.5658914728682171, "std_accuracy": 0.08562295362160666, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.5813953488372093, "std_accuracy": 0.03797658515942914, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.6356589147286822, "std_accuracy": 0.021925791664699188, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.6046511627906977, "std_accuracy": 0.05023829998765782, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.5968992248062015, "std_accuracy": 0.029005096021503425, "total_folds": 3}}, "recommendation": "EXCELLENT - High accuracy (0.651) with statistical significance"}}, "overall_summary": {"overall_accuracy": 0.5865256401563949, "contexts_tested": 2, "statistically_significant_contexts": 2, "system_reliability_score": 1.0, "optimal_ratios_by_context": {"Clay_Set1_Mid": {"historical_ratio": 0.3, "momentum_ratio": 0.7, "accuracy": 0.6530688979591835}, "Clay_Set2_Mid": {"historical_ratio": 0.3, "momentum_ratio": 0.7, "accuracy": 0.6511742790697672}}}, "recommendations": ["GOOD: Balance ratio optimization shows good reliability across contexts.", "ACCURACY: Good accuracy improvement from balance ratio optimization.", "IMPLEMENTATION: Apply context-specific optimal ratios:", "  • Clay_Set1_Mid: 0.3/0.7 (accuracy: 0.653)", "  • Clay_Set2_Mid: 0.3/0.7 (accuracy: 0.651)"]}