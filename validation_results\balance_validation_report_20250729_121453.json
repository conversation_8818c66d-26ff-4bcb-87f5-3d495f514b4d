{"validation_timestamp": "2025-07-29T12:14:53.509741", "validation_type": "historical_momentum_balance_ratios", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 100000, "temporal_validation": true, "min_context_sample_size": 150, "significance_threshold": 0.05, "balance_ratios_tested": [[0.2, 0.8], [0.3, 0.7], [0.4, 0.6], [0.5, 0.5], [0.6, 0.4], [0.7, 0.3], [0.8, 0.2]]}, "context_results": {"Clay_Set1_Mid": {"optimal_balance": {"historical_ratio": 0.8, "momentum_ratio": 0.2, "accuracy": 0.6394108843537416, "confidence_interval_95": [0.6122448979591837, 0.6938775510204082], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 147}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.5714285714285715, "std_accuracy": 0.06008000589338673, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.54421768707483, "std_accuracy": 0.03468720757546111, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.5306122448979592, "std_accuracy": 0.03332639105827453, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.5714285714285714, "std_accuracy": 0.03332639105827453, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.5782312925170068, "std_accuracy": 0.009620500424306781, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.6394557823129251, "std_accuracy": 0.08386957827168676, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.6394557823129252, "std_accuracy": 0.03848200169722707, "total_folds": 3}}, "recommendation": "GOOD - Moderate accuracy (0.639) with statistical significance"}, "Clay_Set2_Mid": {"optimal_balance": {"historical_ratio": 0.7, "momentum_ratio": 0.3, "accuracy": 0.6433606201550388, "confidence_interval_95": [0.627906976744186, 0.6511627906976745], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 129}, "all_ratios_tested": {"0.2_0.8": {"average_accuracy": 0.6124031007751938, "std_accuracy": 0.010962895832349594, "total_folds": 3}, "0.3_0.7": {"average_accuracy": 0.5813953488372093, "std_accuracy": 0.03288868749704873, "total_folds": 3}, "0.4_0.6": {"average_accuracy": 0.5968992248062016, "std_accuracy": 0.10457936095528714, "total_folds": 3}, "0.5_0.5": {"average_accuracy": 0.6124031007751939, "std_accuracy": 0.0790545661022137, "total_folds": 3}, "0.6_0.4": {"average_accuracy": 0.6124031007751938, "std_accuracy": 0.17018991007829573, "total_folds": 3}, "0.7_0.3": {"average_accuracy": 0.6434108527131783, "std_accuracy": 0.010962895832349594, "total_folds": 3}, "0.8_0.2": {"average_accuracy": 0.5658914728682171, "std_accuracy": 0.021925791664699188, "total_folds": 3}}, "recommendation": "GOOD - Moderate accuracy (0.643) with statistical significance"}}, "overall_summary": {"overall_accuracy": 0.5928311523945127, "contexts_tested": 2, "statistically_significant_contexts": 2, "system_reliability_score": 1.0, "optimal_ratios_by_context": {"Clay_Set1_Mid": {"historical_ratio": 0.8, "momentum_ratio": 0.2, "accuracy": 0.6394108843537416}, "Clay_Set2_Mid": {"historical_ratio": 0.7, "momentum_ratio": 0.3, "accuracy": 0.6433606201550388}}}, "recommendations": ["GOOD: Balance ratio optimization shows good reliability across contexts.", "ACCURACY: Good accuracy improvement from balance ratio optimization.", "IMPLEMENTATION: Apply context-specific optimal ratios:", "  • Clay_Set1_Mid: 0.8/0.2 (accuracy: 0.639)", "  • Clay_Set2_Mid: 0.7/0.3 (accuracy: 0.643)"]}