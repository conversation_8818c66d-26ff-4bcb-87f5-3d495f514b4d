#!/usr/bin/env python3
"""
Apply new balance ratios from latest validation results
"""

from enhanced_adaptive_learning_system import EnhancedAdaptiveLearningSystem

def main():
    print("🔄 Applying New Balance Ratios...")
    print("=" * 60)

    # Create enhanced learning system instance
    enhanced_system = EnhancedAdaptiveLearningSystem()

    # Apply the new validation results from latest bootstrap run
    new_validation_results = {
        'status': 'success',
        'context_results': {
            'Clay_Set1_Mid': {
                'optimal_balance': {
                    'historical_ratio': 0.4,
                    'momentum_ratio': 0.6,
                    'accuracy': 0.667,
                    'is_statistically_significant': True
                }
            },
            'Clay_Set2_Mid': {
                'optimal_balance': {
                    'historical_ratio': 0.6,
                    'momentum_ratio': 0.4,
                    'accuracy': 0.650,
                    'is_statistically_significant': True
                }
            }
        }
    }

    print("🎯 Applying new optimal ratios:")
    print("   Clay_Set1_Mid: 0.4/0.6 (66.7% accuracy)")
    print("   Clay_Set2_Mid: 0.6/0.4 (65.0% accuracy)")

    # Apply the new ratios
    application_results = enhanced_system.apply_validated_balance_ratios(new_validation_results)
    
    if application_results.get('status') == 'success':
        print(f"🎉 Successfully applied new optimal ratios!")
        print(f"📝 New configuration version: {application_results['new_version']}")

        # Show what was applied
        changes = application_results.get('changes_applied', {})
        print("\n📋 Applied Changes:")
        for context, ratio in changes.items():
            print(f"   {context}: {ratio}")

    else:
        print(f"❌ Failed to apply: {application_results.get('message', 'Unknown error')}")

    print("\n" + "=" * 60)
    print("✅ Application complete!")

if __name__ == "__main__":
    main()
